<lint-module
    format="1"
    dir="/home/<USER>/colony/src-tauri/gen/android/app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.5.1"
    buildFolder="build"
    bootClassPath="/home/<USER>/Android/Sdk/platforms/android-34/android.jar:/home/<USER>/Android/Sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="universalRelease"/>
</lint-module>
