{"$schema": "https://schema.tauri.app/config/2", "productName": "Colony", "version": "1.1.5", "identifier": "com.colony.gui", "app": {"windows": [{"label": "main", "create": true, "url": "index.html", "dragDropEnabled": false, "center": false, "width": 1024.0, "height": 700.0, "minWidth": 1024.0, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "title": "Colony", "fullscreen": false, "focus": true, "transparent": false, "maximized": true, "visible": true, "decorations": true, "alwaysOnBottom": false, "alwaysOnTop": false, "visibleOnAllWorkspaces": false, "contentProtected": false, "skipTaskbar": false, "titleBarStyle": "Visible", "hiddenTitle": false, "acceptFirstMouse": false, "shadow": true, "incognito": false, "zoomHotkeysEnabled": false, "browserExtensionsEnabled": false, "useHttpsScheme": false, "javascriptDisabled": false, "allowLinkPreview": true, "disableInputAccessoryView": false}], "security": {"freezePrototype": false, "dangerousDisableAssetCspModification": false, "assetProtocol": {"scope": [], "enable": false}, "pattern": {"use": "brownfield"}, "capabilities": []}, "macOSPrivateApi": false, "withGlobalTauri": false, "enableGTKAppId": false}, "build": {"devUrl": "http://localhost:1420/", "frontendDist": "../build", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "removeUnusedCommands": false}, "bundle": {"active": true, "targets": "all", "createUpdaterArtifacts": false, "publisher": "Colony", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon-1024x1024.png", "icons/icon.icns", "icons/icon.ico"], "resources": [], "copyright": "Copyright © 2025 <PERSON>. All rights reserved.", "category": "Utility", "shortDescription": "Colony - Autonomi Semantic Search Engine and File Manager", "longDescription": "A desktop application for interacting with the Autonomi network", "useLocalToolsDir": false, "externalBin": ["../target/binaries/colony-dweb"], "windows": {"digestAlgorithm": "sha256", "certificateThumbprint": null, "timestampUrl": "", "tsp": false, "webviewInstallMode": {"type": "downloadBootstrapper", "silent": true}, "allowDowngrades": true, "wix": {"version": null, "upgradeCode": null, "language": "en-US", "template": null, "fragmentPaths": [], "componentGroupRefs": [], "componentRefs": [], "featureGroupRefs": [], "featureRefs": [], "mergeRefs": [], "enableElevatedUpdateTask": false, "bannerPath": null, "dialogImagePath": null}, "nsis": {"template": null, "headerImage": null, "sidebarImage": null, "installerIcon": null, "installMode": "perMachine", "languages": null, "customLanguageFiles": null, "displayLanguageSelector": true, "compression": "lzma", "startMenuFolder": null, "installerHooks": null, "minimumWebview2Version": null}, "signCommand": null}, "linux": {"appimage": {"bundleMediaFramework": true, "files": {}}, "deb": {"depends": [], "files": {}}, "rpm": {"release": "1", "epoch": 0, "files": {}}}, "macOS": {"frameworks": [], "files": {}, "minimumSystemVersion": "10.13", "signingIdentity": "Developer ID Application: <PERSON> (3364NM68HH)", "hardenedRuntime": true, "providerShortName": "3364NM68HH", "entitlements": "entitlements.plist", "dmg": {"windowSize": {"width": 660, "height": 400}, "appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}}}, "iOS": {"minimumSystemVersion": "13.0"}, "android": {"minSdkVersion": 24}}, "plugins": {}}