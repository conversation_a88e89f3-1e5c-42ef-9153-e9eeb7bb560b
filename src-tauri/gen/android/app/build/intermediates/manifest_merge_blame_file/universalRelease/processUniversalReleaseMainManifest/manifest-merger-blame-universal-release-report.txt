1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.colony.gui"
4    android:versionCode="1001005"
5    android:versionName="1.1.5" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:3:5-67
11-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:3:22-64
12
13    <!-- AndroidTV support -->
14    <uses-feature
14-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:6:5-87
15        android:name="android.software.leanback"
15-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:6:19-59
16        android:required="false" />
16-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:6:60-84
17
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:7:5-81
18-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:7:22-78
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:5-68
19-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:22-65
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:5-77
20-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:22-74
21
22    <permission
22-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
23        android:name="com.colony.gui.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.colony.gui.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
27
28    <application
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
30        android:extractNativeLibs="false"
31        android:icon="@mipmap/ic_launcher"
31-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:9:9-43
32        android:label="@string/app_name"
32-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:10:9-41
33        android:theme="@style/Theme.colony_app"
33-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:11:9-48
34        android:usesCleartextTraffic="false" >
34-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:12:9-63
35        <activity
35-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:13:9-25:20
36            android:name="com.colony.gui.MainActivity"
36-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:17:13-41
37            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
37-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:14:13-129
38            android:exported="true"
38-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:18:13-36
39            android:label="@string/main_activity_title"
39-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:16:13-56
40            android:launchMode="singleTask" >
40-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:15:13-44
41            <intent-filter>
41-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:19:13-24:29
42                <action android:name="android.intent.action.MAIN" />
42-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:20:17-69
42-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:20:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:21:17-77
44-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:21:27-74
45                <!-- AndroidTV support -->
46                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
46-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:23:17-86
46-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:23:27-83
47            </intent-filter>
48        </activity>
49
50        <provider
51            android:name="androidx.core.content.FileProvider"
51-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:28:11-60
52            android:authorities="com.colony.gui.fileprovider"
52-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:29:11-62
53            android:exported="false"
53-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:30:11-35
54            android:grantUriPermissions="true" >
54-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:31:11-45
55            <meta-data
55-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:32:11-34:50
56                android:name="android.support.FILE_PROVIDER_PATHS"
56-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:33:13-63
57                android:resource="@xml/file_paths" />
57-->/home/<USER>/colony/src-tauri/gen/android/app/src/main/AndroidManifest.xml:34:13-47
58        </provider>
59
60        <receiver android:name="app.tauri.notification.TimedNotificationPublisher" />
60-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:9-86
60-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:19-83
61        <receiver android:name="app.tauri.notification.NotificationDismissReceiver" />
61-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:9-87
61-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:19-84
62        <receiver
62-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:14:9-23:20
63            android:name="app.tauri.notification.LocalNotificationRestoreReceiver"
63-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:15:13-83
64            android:directBootAware="true"
64-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:16:13-43
65            android:exported="false" >
65-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:17:13-37
66            <intent-filter>
66-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:18:13-22:29
67                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
67-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:19:17-86
67-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:19:25-83
68                <action android:name="android.intent.action.BOOT_COMPLETED" />
68-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:20:17-79
68-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:20:25-76
69                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
69-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:21:17-82
69-->[:tauri-plugin-notification] /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:21:25-79
70            </intent-filter>
71        </receiver>
72
73        <provider
73-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
74            android:name="androidx.startup.InitializationProvider"
74-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
75            android:authorities="com.colony.gui.androidx-startup"
75-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
76            android:exported="false" >
76-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
77            <meta-data
77-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
78                android:name="androidx.emoji2.text.EmojiCompatInitializer"
78-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
79                android:value="androidx.startup" />
79-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/8.9/transforms/73e3fc9dc9e07171a6ceabcf4fccfea5/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
81                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
81-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/8.9/transforms/73e3fc9dc9e07171a6ceabcf4fccfea5/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
82                android:value="androidx.startup" />
82-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/8.9/transforms/73e3fc9dc9e07171a6ceabcf4fccfea5/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
83        </provider>
84    </application>
85
86</manifest>
