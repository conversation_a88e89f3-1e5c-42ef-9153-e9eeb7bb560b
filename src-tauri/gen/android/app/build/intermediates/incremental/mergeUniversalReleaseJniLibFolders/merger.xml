<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/main/jniLibs"><file name="arm64-v8a/libcolony_app.so" path="/home/<USER>/colony/src-tauri/gen/android/app/src/main/jniLibs/arm64-v8a/libcolony_app.so"/><file name="arm64-v8a/libc++_shared.so" path="/home/<USER>/colony/src-tauri/gen/android/app/src/main/jniLibs/arm64-v8a/libc++_shared.so"/></source></dataSet><dataSet config="universal" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/universal/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/release/jniLibs"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/universalRelease/jniLibs"/></dataSet></merger>