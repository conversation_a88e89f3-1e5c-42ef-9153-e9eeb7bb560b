<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":tauri-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config=":tauri-plugin-shell" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-shell-2.3.0/android/build/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config=":tauri-plugin-opener" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-opener-2.4.0/android/build/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config=":tauri-plugin-notification" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config=":tauri-plugin-dialog" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-dialog-2.3.0/android/build/intermediates/library_assets/release/packageReleaseAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/main/assets"><file name="tauri.conf.json" path="/home/<USER>/colony/src-tauri/gen/android/app/src/main/assets/tauri.conf.json"/></source></dataSet><dataSet config="universal" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/universal/assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/release/assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/src/universalRelease/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/colony/src-tauri/gen/android/app/build/intermediates/shader_assets/universalRelease/compileUniversalReleaseShaders/out"/></dataSet></merger>