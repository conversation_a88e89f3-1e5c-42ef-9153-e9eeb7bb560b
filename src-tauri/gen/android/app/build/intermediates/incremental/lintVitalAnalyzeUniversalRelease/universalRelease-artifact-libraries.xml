<libraries>
  <library
      name=":@@:tauri-android::release"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f47eef540a026ff0c0dbecc7c691cd17/transformed/out/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/f47eef540a026ff0c0dbecc7c691cd17/transformed/out/jars/libs/R.jar"
      resolved="android:tauri-android:unspecified"
      partialResultsDir="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f47eef540a026ff0c0dbecc7c691cd17/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:tauri-plugin-dialog::release"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/b2698d29556f2e0ad30d09ced5a41770/transformed/out/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/b2698d29556f2e0ad30d09ced5a41770/transformed/out/jars/libs/R.jar"
      resolved="android:tauri-plugin-dialog:unspecified"
      partialResultsDir="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-dialog-2.3.0/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/b2698d29556f2e0ad30d09ced5a41770/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:tauri-plugin-notification::release"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e216285c6dd75a3c4d7407f9e3c77a85/transformed/out/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/e216285c6dd75a3c4d7407f9e3c77a85/transformed/out/jars/libs/R.jar"
      resolved="android:tauri-plugin-notification:unspecified"
      partialResultsDir="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-notification-2.3.0/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e216285c6dd75a3c4d7407f9e3c77a85/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:tauri-plugin-opener::release"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e831528768b3c375267b788259f7c000/transformed/out/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/e831528768b3c375267b788259f7c000/transformed/out/jars/libs/R.jar"
      resolved="android:tauri-plugin-opener:unspecified"
      partialResultsDir="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-opener-2.4.0/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e831528768b3c375267b788259f7c000/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:tauri-plugin-shell::release"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e8476e035a666ac2ad009d431b12fda0/transformed/out/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/e8476e035a666ac2ad009d431b12fda0/transformed/out/jars/libs/R.jar"
      resolved="android:tauri-plugin-shell:unspecified"
      partialResultsDir="/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-shell-2.3.0/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e8476e035a666ac2ad009d431b12fda0/transformed/out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.8.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e0ff69047dfd7a9604acc8038cadf51a/transformed/material-1.8.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.8.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e0ff69047dfd7a9604acc8038cadf51a/transformed/material-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/03ab41fc1bdb3e4c1b8001de089f0116/transformed/constraintlayout-2.0.1/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/03ab41fc1bdb3e4c1b8001de089f0116/transformed/constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/01a6fbacc185c6116b2ab18a1c9926a1/transformed/appcompat-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/01a6fbacc185c6116b2ab18a1c9926a1/transformed/appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/5b417f8da847d6f998d65c1d3ee1c99b/transformed/viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/5b417f8da847d6f998d65c1d3ee1c99b/transformed/viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/cf8e38072dffbbacdd6f93a5cdfcf113/transformed/fragment-1.3.6/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/cf8e38072dffbbacdd6f93a5cdfcf113/transformed/fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.6.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f1105d0c5fb8d84ebc48f07a401bb167/transformed/activity-1.6.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.6.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f1105d0c5fb8d84ebc48f07a401bb167/transformed/activity-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/ae5938085e0fb0bb9c901b2ceb887ae6/transformed/lifecycle-viewmodel-savedstate-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/ae5938085e0fb0bb9c901b2ceb887ae6/transformed/lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/ef2bce9693e1d4e896c88b49195cc1da/transformed/savedstate-1.2.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/ef2bce9693e1d4e896c88b49195cc1da/transformed/savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/4ec50487296eaf493fb3276a48e5c3ba/transformed/webkit-1.6.1/jars/classes.jar"
      resolved="androidx.webkit:webkit:1.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/4ec50487296eaf493fb3276a48e5c3ba/transformed/webkit-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/22e77347c7f224a51aa3ec4e19a434bf/transformed/appcompat-resources-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/22e77347c7f224a51aa3ec4e19a434bf/transformed/appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/73e5ebd22abf46f09a6f4890a1cc5978/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/73e5ebd22abf46f09a6f4890a1cc5978/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/5e920f83e897be8b58e0cac4b9064c27/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/5e920f83e897be8b58e0cac4b9064c27/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/7ce87b92d763b5be8bdc000f1d043428/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/7ce87b92d763b5be8bdc000f1d043428/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/7ddc6deecb68b0fccc64231ac07f0b06/transformed/recyclerview-1.1.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/7ddc6deecb68b0fccc64231ac07f0b06/transformed/recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/2c303a426b6b2d610b87f3a3c722cc4b/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/2c303a426b6b2d610b87f3a3c722cc4b/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/eae3392097f16b6c64305fd515d9b218/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/eae3392097f16b6c64305fd515d9b218/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/8608944814c1d23c7376ad868742bce3/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/8608944814c1d23c7376ad868742bce3/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/386073bcc7a66bb951ba9bfc0b85760f/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/386073bcc7a66bb951ba9bfc0b85760f/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/9f8d873241d5ac4067e70d59d2bd9aa2/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/9f8d873241d5ac4067e70d59d2bd9aa2/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/bd15ad410c60a723649883093ecd154b/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/bd15ad410c60a723649883093ecd154b/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/fa31ce120cda1065977785afc13b1ff3/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/fa31ce120cda1065977785afc13b1ff3/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/5f9728a69179ebb1b909770542175c91/transformed/core-ktx-1.9.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/5f9728a69179ebb1b909770542175c91/transformed/core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0/jars/classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/96bf7dbeed0efb34035c0df5e6ae19e2/transformed/core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/d3c4d7d65939b431ad96d294f90c348c/transformed/annotation-experimental-1.4.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/d3c4d7d65939b431ad96d294f90c348c/transformed/annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/d09f4c638e33fbf5c2ca3ed42183e87a/transformed/lifecycle-viewmodel-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/d09f4c638e33fbf5c2ca3ed42183e87a/transformed/lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.6.1/97fd74ccf54a863d221956ffcd21835e168e2aaa/kotlinx-coroutines-core-jvm-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.6.1/4e61fcdcc508cbaa37c4a284a50205d7c7767e37/kotlinx-coroutines-android-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.0/ed04f49e186a116753ad70d34f0ac2925d1d8020/kotlin-stdlib-jdk8-1.8.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.0/3c91271347f678c239607abb676d4032a7898427/kotlin-stdlib-jdk7-1.8.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/f700a2f2b8f0d6d0fde48f56d894dc722fb029d7/kotlin-stdlib-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.25"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f79ae47054c23c24546920ac0e58fc65/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f79ae47054c23c24546920ac0e58fc65/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/4b1426e8fd82acf4f429200f6c560758/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/4b1426e8fd82acf4f429200f6c560758/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/18144f22263cda1016925735a3f19636/transformed/lifecycle-runtime-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/18144f22263cda1016925735a3f19636/transformed/lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/5b83dd23ed6c7f1538fce8b15f25655a/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/5b83dd23ed6c7f1538fce8b15f25655a/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/0d26d6be02695557529fccde2c3ee629/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/0d26d6be02695557529fccde2c3ee629/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/f189b49f2aa83fe09843964819f266c9/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/f189b49f2aa83fe09843964819f266c9/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/dab599b39a39881ec3e0f84cf05dde74/transformed/lifecycle-livedata-core-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/dab599b39a39881ec3e0f84cf05dde74/transformed/lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.5.1/1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12/lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/ac55123d1af949a321abf4d889966bdf/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/ac55123d1af949a321abf4d889966bdf/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/ae0af918abb8fd43eed515eea4ac7dfc/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/ae0af918abb8fd43eed515eea4ac7dfc/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/234fa6d57bbcfe3e4c0b40fe7065f3ec/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/234fa6d57bbcfe3e4c0b40fe7065f3ec/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/6404bcb6653555cf3b904cfaf8f432a9/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/6404bcb6653555cf3b904cfaf8f432a9/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.3.0/21f49f5f9b85fc49de712539f79123119740595/annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.1/30988fe2d77f3fe3bf7551bb8a8b795fad7e7226/constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/e3a8cf6ba8bb382a80861643380bc305/transformed/emoji2-views-helper-1.2.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/e3a8cf6ba8bb382a80861643380bc305/transformed/emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/jars/classes.jar:/home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/83ca34795f7777b4053dca26418062fd/transformed/emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/b9cedbb490e6864995d4eccd83d7e5fe/transformed/browser-1.8.0/jars/classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/b9cedbb490e6864995d4eccd83d7e5fe/transformed/browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/73e3fc9dc9e07171a6ceabcf4fccfea5/transformed/lifecycle-process-2.4.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.4.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/73e3fc9dc9e07171a6ceabcf4fccfea5/transformed/lifecycle-process-2.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.0.0/c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1/concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/44a3500262b1d5f1d2e1e6f8dddf9d6c/transformed/startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/44a3500262b1d5f1d2e1e6f8dddf9d6c/transformed/startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.9/transforms/7a477be6598bb63b7dfc6fc7279cf80a/transformed/tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.9/transforms/7a477be6598bb63b7dfc6fc7279cf80a/transformed/tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.15.0/38c8485a652f808c8c149150da4e5c2b0bd17f9a/error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.fasterxml.jackson.core:jackson-annotations:2.15.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-annotations/2.15.3/79baf4e605eb3bbb60b1c475d44a7aecceea1d60/jackson-annotations-2.15.3.jar"
      resolved="com.fasterxml.jackson.core:jackson-annotations:2.15.3"/>
  <library
      name="com.fasterxml.jackson.core:jackson-core:2.15.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.15.3/60d600567c1862840397bf9ff5a92398edc5797b/jackson-core-2.15.3.jar"
      resolved="com.fasterxml.jackson.core:jackson-core:2.15.3"/>
  <library
      name="com.fasterxml.jackson.core:jackson-databind:2.15.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.15.3/a734bc2c47a9453c4efa772461a3aeb273c010d9/jackson-databind-2.15.3.jar"
      resolved="com.fasterxml.jackson.core:jackson-databind:2.15.3"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
