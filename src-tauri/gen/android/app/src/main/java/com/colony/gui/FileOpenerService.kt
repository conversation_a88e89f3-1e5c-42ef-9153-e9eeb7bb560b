package com.colony.gui

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.os.FileObserver
import android.util.Log
import java.io.File

class FileOpenerService : Service() {
    private var fileObserver: FileObserver? = null
    private val TAG = "FileOpenerService"

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "FileOpenerService created")
        startFileWatcher()
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "FileOpenerService destroyed")
        fileObserver?.stopWatching()
    }

    private fun startFileWatcher() {
        try {
            // Get the communication directory
            val appDataDir = File(applicationContext.filesDir, "file_comm")
            if (!appDataDir.exists()) {
                appDataDir.mkdirs()
            }

            Log.d(TAG, "Watching directory: ${appDataDir.absolutePath}")

            fileObserver = object : FileObserver(appDataDir.absolutePath, CREATE) {
                override fun onEvent(event: Int, path: String?) {
                    if (path != null && path.startsWith("open_request_")) {
                        Log.d(TAG, "Detected file open request: $path")
                        handleFileOpenRequest(File(appDataDir, path))
                    }
                }
            }

            fileObserver?.startWatching()
            Log.d(TAG, "File watcher started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start file watcher: ${e.message}", e)
        }
    }

    private fun handleFileOpenRequest(requestFile: File) {
        try {
            if (!requestFile.exists()) {
                Log.w(TAG, "Request file does not exist: ${requestFile.absolutePath}")
                return
            }

            // Read the file path from the request file
            val filePath = requestFile.readText().trim()
            Log.d(TAG, "Processing file open request for: $filePath")

            // We need an Activity context to open files, but we're in a Service
            // We'll need to get the current activity or use a different approach
            val result = "Error: Cannot open file from service context - need Activity"
            Log.d(TAG, "File open result: $result")

            // Write response to response file
            val responseFileName = requestFile.name.replace("open_request_", "open_response_")
            val responseFile = File(requestFile.parent, responseFileName)
            responseFile.writeText(result)

            Log.d(TAG, "Response written to: ${responseFile.absolutePath}")

        } catch (e: Exception) {
            Log.e(TAG, "Error handling file open request: ${e.message}", e)
            
            // Write error response
            try {
                val responseFileName = requestFile.name.replace("open_request_", "open_response_")
                val responseFile = File(requestFile.parent, responseFileName)
                responseFile.writeText("Error: ${e.message}")
            } catch (writeError: Exception) {
                Log.e(TAG, "Failed to write error response: ${writeError.message}", writeError)
            }
        }
    }
}
