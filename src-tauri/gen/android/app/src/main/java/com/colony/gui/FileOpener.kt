package com.colony.gui

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import java.io.File

class FileOpener {
    companion object {
        @JvmStatic
        fun openFileWithDefaultApp(activity: Activity, filePath: String): String {
            return try {
                android.util.Log.d("FileOpener", "Attempting to open file: $filePath")

                val file = File(filePath)
                if (!file.exists()) {
                    android.util.Log.e("FileOpener", "File does not exist: $filePath")
                    return "Error: File does not exist: $filePath"
                }

                android.util.Log.d("FileOpener", "File exists, size: ${file.length()} bytes")
                android.util.Log.d("FileOpener", "File readable: ${file.canRead()}")
                android.util.Log.d("FileOpener", "File absolute path: ${file.absolutePath}")
                android.util.Log.d("FileOpener", "File parent directory: ${file.parent}")
                android.util.Log.d("FileOpener", "File name: ${file.name}")

                // Check parent directory permissions
                val parentDir = file.parentFile
                if (parentDir != null) {
                    android.util.Log.d("FileOpener", "Parent dir exists: ${parentDir.exists()}")
                    android.util.Log.d("FileOpener", "Parent dir readable: ${parentDir.canRead()}")
                    android.util.Log.d("FileOpener", "Parent dir path: ${parentDir.absolutePath}")
                }

                // Get MIME type from file extension
                val mimeType = getMimeType(file.absolutePath) ?: "*/*"
                android.util.Log.d("FileOpener", "Detected MIME type: $mimeType")

                // Create content URI using FileProvider for security
                val authority = "${activity.packageName}.fileprovider"
                android.util.Log.d("FileOpener", "Using FileProvider authority: $authority")

                val contentUri = try {
                    FileProvider.getUriForFile(
                        activity,
                        authority,
                        file
                    )
                } catch (e: IllegalArgumentException) {
                    android.util.Log.e("FileOpener", "FileProvider failed: ${e.message}")
                    android.util.Log.e("FileOpener", "File path: ${file.absolutePath}")
                    android.util.Log.e("FileOpener", "Trying file:// URI as fallback")

                    // Fallback to file:// URI (less secure but might work for debugging)
                    Uri.fromFile(file)
                }
                android.util.Log.d("FileOpener", "Created content URI: $contentUri")

                // Create intent to open file
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(contentUri, mimeType)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                android.util.Log.d("FileOpener", "Created intent with action: ${intent.action}")
                android.util.Log.d("FileOpener", "Intent data: ${intent.data}")
                android.util.Log.d("FileOpener", "Intent type: ${intent.type}")

                // Check if there's an app that can handle this intent
                val resolveInfo = intent.resolveActivity(activity.packageManager)
                if (resolveInfo != null) {
                    android.util.Log.d("FileOpener", "Found app to handle intent: ${resolveInfo.packageName}")
                    activity.startActivity(intent)
                    "File opened successfully"
                } else {
                    android.util.Log.d("FileOpener", "No direct app found, trying chooser")
                    // If no app can handle the file, show a chooser
                    val chooserIntent = Intent.createChooser(intent, "Open file with...")
                    val chooserResolveInfo = chooserIntent.resolveActivity(activity.packageManager)
                    if (chooserResolveInfo != null) {
                        android.util.Log.d("FileOpener", "Showing chooser dialog")
                        activity.startActivity(chooserIntent)
                        "File opened with chooser"
                    } else {
                        android.util.Log.e("FileOpener", "No application found to open this file type")
                        "Error: No application found to open this file type"
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("FileOpener", "Exception opening file: ${e.message}", e)
                "Error: Failed to open file: ${e.message} (${e.javaClass.simpleName})"
            }
        }

        @JvmStatic
        fun testFileProviderAccess(activity: Activity): String {
            return try {
                // Test with a known Downloads directory path
                val testPath = "/storage/emulated/0/Download"
                val testDir = File(testPath)

                android.util.Log.d("FileOpener", "Testing FileProvider access")
                android.util.Log.d("FileOpener", "Test directory: $testPath")
                android.util.Log.d("FileOpener", "Directory exists: ${testDir.exists()}")
                android.util.Log.d("FileOpener", "Directory readable: ${testDir.canRead()}")

                if (testDir.exists()) {
                    val files = testDir.listFiles()
                    android.util.Log.d("FileOpener", "Files in directory: ${files?.size ?: 0}")

                    files?.take(3)?.forEach { file ->
                        android.util.Log.d("FileOpener", "Found file: ${file.name}")
                        try {
                            val uri = FileProvider.getUriForFile(
                                activity,
                                "${activity.packageName}.fileprovider",
                                file
                            )
                            android.util.Log.d("FileOpener", "FileProvider URI for ${file.name}: $uri")
                        } catch (e: Exception) {
                            android.util.Log.e("FileOpener", "FileProvider failed for ${file.name}: ${e.message}")
                        }
                    }
                }

                "FileProvider test completed - check logs"
            } catch (e: Exception) {
                android.util.Log.e("FileOpener", "FileProvider test failed: ${e.message}", e)
                "FileProvider test failed: ${e.message}"
            }
        }

        @JvmStatic
        private fun getMimeType(filePath: String): String? {
            android.util.Log.d("FileOpener", "Getting MIME type for: $filePath")
            val extension = MimeTypeMap.getFileExtensionFromUrl(filePath)
            android.util.Log.d("FileOpener", "Extracted extension: $extension")

            return if (extension != null) {
                val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
                android.util.Log.d("FileOpener", "MIME type from extension '$extension': $mimeType")
                mimeType
            } else {
                android.util.Log.d("FileOpener", "No extension found, returning null")
                null
            }
        }
    }
}
