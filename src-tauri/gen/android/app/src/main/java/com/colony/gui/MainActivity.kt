package com.colony.gui

import android.content.Intent
import android.os.Bundle
import java.io.File
import java.net.ServerSocket
import java.net.Socket

class MainActivity : TauriActivity() {

    private var socketServerThread: Thread? = null
    private var isServerRunning = false
    private var serverSocket: ServerSocket? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Start Unix socket server
        startSocketServer()

        android.util.Log.d("MainActivity", "Socket server started")
    }

    override fun onDestroy() {
        super.onDestroy()

        // Stop socket server
        stopSocketServer()

        android.util.Log.d("MainActivity", "Socket server stopped")
    }

    private fun startSocketServer() {
        isServerRunning = true
        socketServerThread = Thread {
            try {
                // Create TCP server socket on localhost port 8765
                serverSocket = ServerSocket(8765)

                android.util.Log.d("MainActivity", "TCP server listening on: 127.0.0.1:8765")

                while (isServerRunning) {
                    try {
                        // Accept incoming connections
                        val clientSocket = serverSocket?.accept()
                        if (clientSocket != null) {
                            // Handle client in a separate thread to avoid blocking
                            Thread {
                                handleSocketClient(clientSocket)
                            }.start()
                        }
                    } catch (e: Exception) {
                        if (isServerRunning) {
                            android.util.Log.e("MainActivity", "Error accepting socket connection: ${e.message}", e)
                            Thread.sleep(1000) // Wait before retrying
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Socket server error: ${e.message}", e)
            }
        }
        socketServerThread?.start()
    }

    private fun stopSocketServer() {
        isServerRunning = false
        try {
            serverSocket?.close()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error closing server socket: ${e.message}", e)
        }
        socketServerThread?.interrupt()
        socketServerThread = null
    }

    private fun handleSocketClient(clientSocket: Socket) {
        try {
            val inputStream = clientSocket.inputStream
            val outputStream = clientSocket.outputStream

            // Read the file path from the socket
            val buffer = ByteArray(1024)
            val bytesRead = inputStream.read(buffer)
            if (bytesRead > 0) {
                val filePath = String(buffer, 0, bytesRead).trim()
                android.util.Log.d("MainActivity", "Processing file open request for: $filePath")

                // Use FileOpener to open the file (we have Activity context)
                val result = FileOpener.openFileWithDefaultApp(this, filePath)
                android.util.Log.d("MainActivity", "File open result: $result")

                // Send response back through socket
                outputStream.write(result.toByteArray())
                outputStream.flush()
            }

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error handling socket client: ${e.message}", e)

            // Try to send error response
            try {
                val errorResponse = "Error: ${e.message}"
                clientSocket.outputStream.write(errorResponse.toByteArray())
                clientSocket.outputStream.flush()
            } catch (writeError: Exception) {
                android.util.Log.e("MainActivity", "Failed to write error response: ${writeError.message}", writeError)
            }
        } finally {
            try {
                clientSocket.close()
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error closing client socket: ${e.message}", e)
            }
        }
    }
}