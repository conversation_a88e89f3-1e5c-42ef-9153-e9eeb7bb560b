package com.colony.gui

import android.content.Intent
import android.os.Bundle
import java.io.File

class MainActivity : TauriActivity() {

    private var fileWatcherThread: Thread? = null
    private var isWatching = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Start file watcher thread
        startFileWatcher()

        android.util.Log.d("MainActivity", "File watcher started")
    }

    override fun onDestroy() {
        super.onDestroy()

        // Stop file watcher
        stopFileWatcher()

        android.util.Log.d("MainActivity", "File watcher stopped")
    }

    private fun startFileWatcher() {
        isWatching = true
        fileWatcherThread = Thread {
            try {
                // Get the communication directory
                val appDataDir = File(filesDir, "file_comm")
                if (!appDataDir.exists()) {
                    appDataDir.mkdirs()
                }

                android.util.Log.d("MainActivity", "Watching directory: ${appDataDir.absolutePath}")

                while (isWatching) {
                    try {
                        // Check for request files
                        val requestFiles = appDataDir.listFiles { file ->
                            file.name.startsWith("open_request_") && file.name.endsWith(".txt")
                        }

                        requestFiles?.forEach { requestFile ->
                            handleFileOpenRequest(requestFile)
                        }

                        // Sleep for a short time before checking again
                        Thread.sleep(100)
                    } catch (e: Exception) {
                        android.util.Log.e("MainActivity", "Error in file watcher loop: ${e.message}", e)
                        Thread.sleep(1000) // Wait longer on error
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "File watcher thread error: ${e.message}", e)
            }
        }
        fileWatcherThread?.start()
    }

    private fun stopFileWatcher() {
        isWatching = false
        fileWatcherThread?.interrupt()
        fileWatcherThread = null
    }

    private fun handleFileOpenRequest(requestFile: File) {
        try {
            if (!requestFile.exists()) {
                android.util.Log.w("MainActivity", "Request file does not exist: ${requestFile.absolutePath}")
                return
            }

            // Read the file path from the request file
            val filePath = requestFile.readText().trim()
            android.util.Log.d("MainActivity", "Processing file open request for: $filePath")

            // Use FileOpener to open the file (now we have Activity context)
            val result = FileOpener.openFileWithDefaultApp(this, filePath)
            android.util.Log.d("MainActivity", "File open result: $result")

            // Write response to response file
            val responseFileName = requestFile.name.replace("open_request_", "open_response_")
            val responseFile = File(requestFile.parent, responseFileName)
            responseFile.writeText(result)

            android.util.Log.d("MainActivity", "Response written to: ${responseFile.absolutePath}")

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error handling file open request: ${e.message}", e)

            // Write error response
            try {
                val responseFileName = requestFile.name.replace("open_request_", "open_response_")
                val responseFile = File(requestFile.parent, responseFileName)
                responseFile.writeText("Error: ${e.message}")
            } catch (writeError: Exception) {
                android.util.Log.e("MainActivity", "Failed to write error response: ${writeError.message}", writeError)
            }
        }
    }
}