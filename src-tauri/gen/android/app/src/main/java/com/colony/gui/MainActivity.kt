package com.colony.gui

import android.os.Bundle

class MainActivity : TauriActivity() {

    companion object {
        // Static reference to the current activity for JNI calls
        @Volatile
        private var currentActivity: MainActivity? = null

        @JvmStatic
        fun getCurrentActivity(): MainActivity? = currentActivity

        // Static method that can be called from JNI without needing an instance
        @JvmStatic
        fun openFileWithDefaultAppStatic(filePath: String): String {
            val activity = currentActivity
            return if (activity != null) {
                FileOpener.openFileWithDefaultApp(activity, filePath)
            } else {
                "Error: No active activity available"
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        currentActivity = this
    }

    override fun onResume() {
        super.onResume()
        currentActivity = this
    }

    override fun onDestroy() {
        super.onDestroy()
        if (currentActivity == this) {
            currentActivity = null
        }
    }
}