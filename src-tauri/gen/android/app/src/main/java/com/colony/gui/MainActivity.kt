package com.colony.gui

import android.os.Bundle

class MainActivity : TauriActivity() {

    companion object {
        // Static reference to the current activity for JNI calls
        @Volatile
        private var currentActivity: MainActivity? = null

        @JvmStatic
        fun getCurrentActivity(): MainActivity? = currentActivity
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        currentActivity = this
    }

    override fun onDestroy() {
        super.onDestroy()
        currentActivity = null
    }

    // Method that can be called from JNI/Rust
    fun openFileWithDefaultApp(filePath: String): String {
        return FileOpener.openFileWithDefaultApp(this, filePath)
    }
}