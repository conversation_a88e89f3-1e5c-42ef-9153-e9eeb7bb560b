import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the dependencies
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}));

vi.mock('@tauri-apps/plugin-opener', () => ({
  openPath: vi.fn()
}));

vi.mock('../../stores/toast', () => ({
  addToast: vi.fn()
}));

// Mock navigator.userAgent for platform detection
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36'
  },
  writable: true
});

describe('openFileWithDefaultApp', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should detect Android platform correctly', () => {
    // The Android detection logic
    const isAndroid = typeof window !== 'undefined' && /Android/i.test(navigator.userAgent);
    expect(isAndroid).toBe(true);
  });

  it('should detect desktop platform correctly', () => {
    // Mock desktop user agent
    Object.defineProperty(window, 'navigator', {
      value: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      writable: true
    });

    const isAndroid = typeof window !== 'undefined' && /Android/i.test(navigator.userAgent);
    expect(isAndroid).toBe(false);
  });
});
